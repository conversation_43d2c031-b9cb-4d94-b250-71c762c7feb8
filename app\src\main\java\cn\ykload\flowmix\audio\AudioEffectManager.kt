package cn.ykload.flowmix.audio

import android.media.audiofx.DynamicsProcessing
import android.util.Log
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.EqBand
import cn.ykload.flowmix.utils.Constants

/**
 * 音频效果管理器
 * 负责管理全局音频效果，使用DynamicsProcessing API实现AutoEq
 */
class AudioEffectManager {
    
    companion object {
        private const val TAG = "AudioEffectManager"
        private const val EFFECT_PRIORITY = 0
        private const val CHANNEL_COUNT = 2 // 立体声

        // DynamicsProcessing配置常量
        private const val VARIANT = DynamicsProcessing.VARIANT_FAVOR_FREQUENCY_RESOLUTION
        private const val PRE_EQ_IN_USE = true
        private const val MBC_IN_USE = false
        private const val POST_EQ_IN_USE = false
        private const val LIMITER_IN_USE = false
    }
    
    private var dynamicsProcessing: DynamicsProcessing? = null
    private var isEffectEnabled = false
    private var currentEqData: AutoEqData? = null
    
    /**
     * 应用AutoEq配置
     * @param autoEqData AutoEq数据
     * @return 是否成功应用
     */
    fun applyAutoEq(autoEqData: AutoEqData): Boolean {
        return try {
            Log.d(TAG, "开始应用AutoEq配置: ${autoEqData.name}")
            Log.d(TAG, "频段数量: ${autoEqData.bands.size}")
            
            // 释放之前的效果器
            releaseEffect()
            
            // 创建DynamicsProcessing配置
            val config = createDynamicsProcessingConfig(autoEqData.bands)
            
            // 创建DynamicsProcessing实例
            dynamicsProcessing = DynamicsProcessing(
                EFFECT_PRIORITY,
                Constants.GLOBAL_AUDIO_SESSION_ID,
                config
            )
            
            // 启用效果器
            dynamicsProcessing?.setEnabled(true)
            isEffectEnabled = true
            currentEqData = autoEqData
            
            Log.d(TAG, "AutoEq配置应用成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "应用AutoEq配置失败", e)
            releaseEffect()
            false
        }
    }
    
    /**
     * 创建DynamicsProcessing配置
     */
    private fun createDynamicsProcessingConfig(bands: List<EqBand>): DynamicsProcessing.Config {
        // 计算需要的频段数量
        val bandCount = bands.size

        Log.d(TAG, "创建DynamicsProcessing配置，频段数: $bandCount")

        // 创建配置构建器
        val configBuilder = DynamicsProcessing.Config.Builder(
            VARIANT,
            CHANNEL_COUNT,
            PRE_EQ_IN_USE,
            bandCount, // PreEQ频段数
            MBC_IN_USE,
            0, // MBC频段数
            POST_EQ_IN_USE,
            0, // PostEQ频段数
            LIMITER_IN_USE
        )

        // 创建PreEQ配置
        val preEq = DynamicsProcessing.Eq(true, true, bandCount)

        // 配置PreEQ频段
        bands.forEachIndexed { index, band ->
            try {
                // 创建EQ频段配置
                val eqBand = DynamicsProcessing.EqBand(
                    true, // enabled
                    band.frequency, // cutoffFrequency
                    band.gain // gain
                )

                // 设置频段
                preEq.setBand(index, eqBand)

                Log.v(TAG, "配置频段 $index: ${band.frequency}Hz, ${band.gain}dB")
            } catch (e: Exception) {
                Log.w(TAG, "配置频段 $index 失败: ${e.message}")
            }
        }

        // 为每个声道设置相同的PreEQ配置
        for (channel in 0 until CHANNEL_COUNT) {
            try {
                configBuilder.setPreEqByChannelIndex(channel, preEq)
            } catch (e: Exception) {
                Log.w(TAG, "设置声道 $channel 的PreEQ失败: ${e.message}")
            }
        }

        return configBuilder.build()
    }
    
    /**
     * 启用/禁用音频效果
     */
    fun setEnabled(enabled: Boolean): Boolean {
        return try {
            dynamicsProcessing?.setEnabled(enabled)
            isEffectEnabled = enabled
            Log.d(TAG, "音频效果${if (enabled) "启用" else "禁用"}成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置音频效果状态失败", e)
            false
        }
    }
    
    /**
     * 释放音频效果资源
     */
    fun releaseEffect() {
        try {
            dynamicsProcessing?.release()
            dynamicsProcessing = null
            isEffectEnabled = false
            currentEqData = null
            Log.d(TAG, "音频效果资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放音频效果资源失败", e)
        }
    }
    
    /**
     * 检查是否支持DynamicsProcessing
     */
    fun isDynamicsProcessingSupported(): Boolean {
        return try {
            val testEffect = DynamicsProcessing(
                EFFECT_PRIORITY,
                Constants.GLOBAL_AUDIO_SESSION_ID,
                createMinimalConfig()
            )
            testEffect.release()
            true
        } catch (e: Exception) {
            Log.w(TAG, "设备不支持DynamicsProcessing: ${e.message}")
            false
        }
    }
    
    /**
     * 创建最小配置用于测试
     */
    private fun createMinimalConfig(): DynamicsProcessing.Config {
        return DynamicsProcessing.Config.Builder(
            VARIANT,
            CHANNEL_COUNT,
            false, // PreEQ
            0,
            false, // MBC
            0,
            false, // PostEQ
            0,
            false  // Limiter
        ).build()
    }
    
    /**
     * 获取当前效果状态
     */
    fun isEnabled(): Boolean = isEffectEnabled
    
    /**
     * 获取当前EQ数据
     */
    fun getCurrentEqData(): AutoEqData? = currentEqData
}
